
(defun ai-auto-complete-tools-parse-tool-code-args (args-str)
  "Parse tool_code arguments with format: key1=\\\"value1\\\", key2=\\\"value2\\\"
Values are always delimited by \\\" (three backslashes + quote).
Finds ALL key-value pairs in the string, handling commas inside values correctly."
  (let ((result '())
        (pos 0))
    ;; Use string-match-p to find all occurrences of key=\"value\" pattern
    (while (string-match "\\([a-zA-Z_][a-zA-Z0-9_]*\\)[ \t]*=[ \t]*\\\\\"\\([^\\]*?\\(?:\\\\.[^\\]*?\\)*\\)\\\\\"" args-str pos)
      (let ((key (match-string 1 args-str))
            (value (match-string 2 args-str)))
        ;; Process escape sequences in the value
        (setq value (replace-regexp-in-string "\\\\\\\\n" "\n" value))    ; \\n -> \n
        (setq value (replace-regexp-in-string "\\\\\\\\\"" "\"" value))    ; \\" -> "
        (setq value (replace-regexp-in-string "\\\\\\\\\\\\\\\\" "\\\\" value))    ; \\\\ -> \\
        (push (cons key value) result)
        ;; Move position past this match to find the next one
        (setq pos (match-end 0))))
    (nreverse result)))


(ai-auto-complete-tools-parse-tool-code-args tools-code-strings)
(ai-auto-complete-tools-parse-tool-code-args test-data)


(defvar test-data "path = \\\"my_file.txt\\\", content = \\\"But code ain't all sunshine, sometimes it's a bitch,\\\\nLike a broken pipe, or a whore with a glitch.\\\\nYou debug and you sweat, you curse and you pray,\\\\nHoping the damn thing works by the end of the day.\\\\n\\\\nAnd when it finally runs, smooth as can be,\\\\nYou feel like a god, sipping on your tea.\\\\nBut don't get too cocky, the bugs will return,\\\\nLike old debts, they always come back to burn.\\\"")

(defvar tools-code-strings "filename=\\\"my_file.txt\\\"")
