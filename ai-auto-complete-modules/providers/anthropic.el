;;; anthropic.el --- Anthropic provider for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides the Anthropic provider implementation for the AI Auto Complete package.

;;; Code:

(require 'request)
(require 'json)
(require 'core/backend)
(require 'customization/model-management)

;; Helper function to trim whitespace (replacement for s-trim)
(defun ai-auto-complete-anthropic--trim (str)
  "Simple trim function to remove leading and trailing whitespace."
  (when (stringp str)
    (replace-regexp-in-string "\\`[ \t\n\r]+" ""
                             (replace-regexp-in-string "[ \t\n\r]+\\'" "" str))))

;; Helper function to infer JSON schema type from parameter description
(defun ai-auto-complete-anthropic--infer-param-type (param-name param-desc)
  "Infer JSON schema type from PARAM-NAME and PARAM-DESC.
Returns a plist with :type and optionally :items or :properties."
  (let ((desc-lower (downcase param-desc))
        (name-lower (downcase param-name)))
    (cond
     ;; Path parameters are always strings (even if description mentions "list")
     ((or (string-match-p "\\bpath\\b" name-lower)
          (string-match-p "\\bfile\\b\\|\\bdirectory\\b\\|\\bfolder\\b" desc-lower))
      `(:type "string"))

     ;; Boolean types - check parameter name and description
     ((or (string-match-p "\\bbool\\b\\|\\btrue\\b\\|\\bfalse\\b" desc-lower)
          (string-match-p "whether\\|enable\\|disable\\|flag" desc-lower)
          (string-match-p "recursive\\|all\\|force" name-lower))
      `(:type "boolean"))

     ;; Number types
     ((or (string-match-p "\\bnumber\\b\\|\\binteger\\b\\|\\bcount\\b" desc-lower)
          (string-match-p "timeout\\|seconds\\|size\\|length\\|line.*number" desc-lower)
          (string-match-p "start.*line\\|end.*line\\|line" name-lower))
      `(:type "integer"))

     ;; Array/list types (but not paths!)
     ((and (not (string-match-p "\\bpath\\b" name-lower))
           (or (string-match-p "\\blist of\\b\\|\\barray of\\b\\|\\bedits\\b" desc-lower)
               (string-match-p "multiple\\|sequence\\|collection" desc-lower)
               (string-match-p "^list\\b" desc-lower))) ; "List of..." but not "list directory"
      (cond
       ;; List of edit operations (complex objects)
       ((string-match-p "edit.*operations\\|operations.*edit" desc-lower)
        `(:type "array"
          :items (:type "object"
                  :properties (:type (:type "string" :description "Type of edit operation")
                              :parameters (:type "object" :description "Parameters for the operation")))))
       ;; Generic list of strings
       (t `(:type "array" :items (:type "string")))))

     ;; Object types
     ((string-match-p "\\bobject\\b\\|\\bparameters\\b\\|\\bconfig\\b" desc-lower)
      `(:type "object"))

     ;; Default to string
     (t `(:type "string")))))

;; Helper function to convert tool definitions to Anthropic's native format
(defun ai-auto-complete-anthropic--convert-tools-to-native-format (allowed-tools)
  "Convert tool definitions to Anthropic's native function calling format.
ALLOWED-TOOLS is a list of tool names that should be included."
  (let ((anthropic-tools '()))
    (when allowed-tools
      (dolist (tool-name allowed-tools)
        (let ((tool (gethash tool-name ai-auto-complete-tools)))
          (when tool
            (let* ((description (plist-get tool :description))
                   (parameters (plist-get tool :parameters))
                   (anthropic-properties '())
                   (required-params '()))

              ;; Convert parameters to Anthropic's JSON schema format with type inference
              (dolist (param parameters)
                (let* ((param-name (car param))
                       (param-desc (cdr param))
                       (type-info (ai-auto-complete-anthropic--infer-param-type param-name param-desc))
                       (param-type (plist-get type-info :type))
                       (param-schema `((type . ,param-type)
                                      (description . ,param-desc))))

                  ;; Add additional schema properties for complex types
                  (when (string= param-type "array")
                    (let ((items (plist-get type-info :items)))
                      (when items
                        (setq param-schema (append param-schema `((items . ,items)))))))

                  (when (string= param-type "object")
                    (let ((properties (plist-get type-info :properties)))
                      (when properties
                        (setq param-schema (append param-schema `((properties . ,properties)))))))

                  (push `(,(intern param-name) . ,param-schema) anthropic-properties)

                  ;; For now, treat all parameters as required
                  ;; TODO: Could be enhanced to support optional parameters
                  (push param-name required-params)))

              ;; Create the Anthropic function definition
              (let ((anthropic-function
                     `((name . ,tool-name)
                       (description . ,description)
                       (input_schema . ((type . "object")
                                       (properties . ,(nreverse anthropic-properties))
                                       (required . ,(vconcat (nreverse required-params))))))))
                (push anthropic-function anthropic-tools))))))

    (when anthropic-tools
      (message "DEBUG-ANTHROPIC-TOOLS: Converted %d tools to native format with type inference" (length anthropic-tools)))

    (nreverse anthropic-tools))))

;; Helper function to detect if tool result is in structured format for native function calling
(defun ai-auto-complete-anthropic--is-structured-tool-result (content)
  "Check if CONTENT is a structured tool result for native function calling.
Returns t if the content appears to be structured tool results, nil otherwise."
  (and (stringp content)
       (or (string-match-p "^\\[{.*}\\]$" (ai-auto-complete-anthropic--trim content))  ; JSON array format
           (string-match-p "^{.*}$" (ai-auto-complete-anthropic--trim content))         ; Single JSON object
           (string-match-p "STRUCTURED-TOOL-RESULTS:" content)))) ; Explicit marker

;; Helper function to parse structured tool results for native function calling
(defun ai-auto-complete-anthropic--parse-structured-tool-results (content)
  "Parse structured tool results CONTENT into Anthropic tool_result format.
Returns a list of tool_result objects."
  (let ((results '())
        (trimmed-content (ai-auto-complete-anthropic--trim content)))

    (cond
     ;; Handle explicit structured format marker
     ((string-match-p "STRUCTURED-TOOL-RESULTS:" trimmed-content)
      (let ((json-part (replace-regexp-in-string "^.*STRUCTURED-TOOL-RESULTS:\\s-*" "" trimmed-content)))
        (condition-case err
            (let ((parsed-results (json-read-from-string json-part)))
              ;; parsed-results is a vector of internal structured results
              ;; e.g., [{"tool_use_id": "id1", "name": "tool_name", "response": {"result": "output" or "error": true, "message": "err"}}]
              (if (vectorp parsed-results)
                  ;; Array of results
                  (dotimes (i (length parsed-results))
                    (let* ((internal-result (aref parsed-results i))
                           (tool-use-id (or (cdr (assoc 'tool_use_id internal-result)) (format "toolu_missing_id_%d" i)))
                           (response-obj (cdr (assoc 'response internal-result)))
                           (is-error (cdr (assoc 'error response-obj)))
                           (result-val (if is-error (cdr (assoc 'message response-obj)) (cdr (assoc 'result response-obj))))
                           (content-string (if (stringp result-val) result-val (format "%S" result-val))))
                      (push `((type . "tool_result")
                              (tool_use_id . ,tool-use-id)
                              (content . ,content-string)
                              ,@(when is-error '((is_error . t))))
                            results)))
                ;; Single result
                (when (listp parsed-results) ; Assuming parsed-results is a single internal structured result object
                  (let* ((internal-result parsed-results)
                         (tool-use-id (or (cdr (assoc 'tool_use_id internal-result)) "toolu_missing_id_0"))
                         (response-obj (cdr (assoc 'response internal-result)))
                         (is-error (cdr (assoc 'error response-obj)))
                         (result-val (if is-error (cdr (assoc 'message response-obj)) (cdr (assoc 'result response-obj))))
                         (content-string (if (stringp result-val) result-val (format "%S" result-val))))
                    (push `((type . "tool_result")
                            (tool_use_id . ,tool-use-id)
                            (content . ,content-string)
                            ,@(when is-error '((is_error . t))))
                          results)))))
          (error
           (message "Error parsing STRUCTURED-TOOL-RESULTS for Anthropic: %s" (error-message-string err))))))

     ;; Handle JSON array format
     ((string-match-p "^\\[{.*}\\]$" trimmed-content)
      (condition-case err
          (let ((parsed-array (json-read-from-string trimmed-content)))
            (when (vectorp parsed-array)
              (dotimes (i (length parsed-array))
                (let ((result (aref parsed-array i)))
                  ;; This case assumes parsed-array contains internal structured results
                  (let* ((tool-use-id (or (cdr (assoc 'tool_use_id result)) (format "toolu_json_array_id_%d" i)))
                         (response-obj (cdr (assoc 'response result)))
                         (is-error (cdr (assoc 'error response-obj)))
                         (result-val (if is-error (cdr (assoc 'message response-obj)) (cdr (assoc 'result response-obj))))
                         (content-string (if (stringp result-val) result-val (format "%S" result-val))))
                  (push `((type . "tool_result")
                          (tool_use_id . ,tool-use-id)
                          (content . ,content-string)
                          ,@(when is-error '((is_error . t))))
                        results))))))
        (error
         (message "Error parsing JSON array tool results: %s" (error-message-string err)))))

     ;; Handle single JSON object format
     ((string-match-p "^{.*}$" trimmed-content)
      (condition-case err
          (let ((parsed-object (json-read-from-string trimmed-content)))
            (when (and (listp parsed-object) (assoc 'name parsed-object) (assoc 'response parsed-object))
              ;; This case assumes parsed-object is a single internal structured result
              (let* ((tool-use-id (or (cdr (assoc 'tool_use_id parsed-object)) "toolu_json_obj_id_0"))
                     (response-obj (cdr (assoc 'response parsed-object)))
                     (is-error (cdr (assoc 'error response-obj)))
                     (result-val (if is-error (cdr (assoc 'message response-obj)) (cdr (assoc 'result response-obj))))
                     (content-string (if (stringp result-val) result-val (format "%S" result-val))))
              (push `((type . "tool_result")
                      (tool_use_id . ,tool-use-id)
                      (content . ,content-string)
                      ,@(when is-error '((is_error . t))))
                    results))))
        (error
         (message "Error parsing JSON object tool results: %s" (error-message-string err)))))

     ;; Fallback: try to parse as XML and convert to structured format
     (t
      (let ((xml-results (ai-auto-complete-anthropic--parse-internal-tool-result content)))
        (dotimes (i (length xml-results))
          (let* ((xml-result (nth i xml-results))
                 ;; (tool-name (car xml-result)) ; Name not directly used in tool_result block
                 (tool-output (cdr xml-result)))
            (push `((type . "tool_result")
                    (tool_use_id . ,(format "toolu_xml_id_%d" i)) ; Placeholder ID for XML results
                    (content . ,tool-output)) ; tool-output is already a string
                  results)))))))

    (nreverse results)))

;; Helper function to parse the 'tool-result' string from history
;; This is a simplified parser; a more robust system would store structured tool data.
(defun ai-auto-complete-anthropic--parse-internal-tool-result (tool-result-string)
  "Robustly parse a string potentially containing multiple <tool_result> XML blocks.
Returns a list of (cons tool-name . tool-content-string), or nil if input is empty."
  (let ((results '())
        (start 0)
        (trimmed-input (ai-auto-complete-anthropic--trim tool-result-string))) ; Trim once at the beginning

    ;; If the input is empty after trimming, return nil immediately.
    (if (string-empty-p trimmed-input)
        nil
      (progn
        ;; Attempt to parse <tool_result...> tags
        ;; Regex updated for non-greedy multi-line content within tool_result
        (while (string-match "<tool_result name=\"\\([^\"]+\\)\">[[:space:]]*\\(\\(?:.\\|\n\\)*?\\)[[:space:]]*</tool_result>" trimmed-input start)
          (let ((tool-name (match-string 1 trimmed-input))
                (tool-content (ai-auto-complete-anthropic--trim (match-string 2 trimmed-input)))) ; Content inside tool_result
            (push (cons tool-name tool-content) results))
          (setq start (match-end 0)))

        ;; If no <tool_result> tags were found by the primary regex,
        ;; and results list is still empty, try the simpler "Tool: name\nResult: content" format.
        ;; This part assumes only one such block if this format is used.
        (when (null results)
          (if (string-match "Tool: \\([^\n]+\\)\nResult: \\(\\(?:.\\|\n\\)*\\)" trimmed-input) ; Match multi-line result
              (push (cons (ai-auto-complete-anthropic--trim (match-string 1 trimmed-input))
                          (ai-auto-complete-anthropic--trim (match-string 2 trimmed-input)))
                    results)))

        ;; If still no results from specific parsers, and the input was not empty,
        ;; treat the whole original trimmed string as output of an "unknown_tool_operation".
        ;; This ensures that if there was content, we try to send something.
        (when (and (null results) (not (string-empty-p trimmed-input)))
            (push (cons "unknown_tool_operation" trimmed-input) results))

        (nreverse results))))) ; Return in the order they appeared, or nil if input was empty.

;; Tool code argument parsing function (shared with Gemini)
(defun ai-auto-complete-anthropic-parse-tool-code-args (args-str)
  "Parse tool_code arguments with format: key1=\\\"value1\\\", key2=\\\"value2\\\"
Values are always delimited by \\\" (three backslashes + quote).
Finds ALL key-value pairs in the string, handling commas inside values correctly."
  (let ((result '())
        (pos 0))
    ;; Use string-match-p to find all occurrences of key=\"value\" pattern
    (while (string-match "\\([a-zA-Z_][a-zA-Z0-9_]*\\)[ \t]*=[ \t]*\\\\\"\\([^\\]*?\\(?:\\\\.[^\\]*?\\)*\\)\\\\\"" args-str pos)
      (let ((key (match-string 1 args-str))
            (value (match-string 2 args-str)))
        ;; Process escape sequences in the value
        (setq value (replace-regexp-in-string "\\\\\\\\n" "\n" value))    ; \\n -> \n
        (setq value (replace-regexp-in-string "\\\\\\\\\"" "\"" value))    ; \\" -> "
        (setq value (replace-regexp-in-string "\\\\\\\\\\\\\\\\" "\\\\" value))    ; \\\\ -> \\
        (push (cons key value) result)
        ;; Move position past this match to find the next one
        (setq pos (match-end 0))))
    (nreverse result)))

;; Anthropic provider implementation
(defun ai-auto-complete-anthropic-provider (history callback model system-prompt &optional agent-name)
  "Request completion from Anthropic API with HISTORY, and call CALLBACK with the result.
Uses MODEL and SYSTEM-PROMPT for the request. AGENT-NAME is the optional name of the agent making the request."
  (message "Requesting completion from Anthropic API with model %s" model)
  (message "Using system prompt: %s" (substring system-prompt 0 (length system-prompt)))
  (when agent-name
    (message "Request is for agent: %s" agent-name))
  (if (string-empty-p ai-auto-complete-anthropic-api-key)
      (progn
        (message "Anthropic API key is not set")
        (funcall callback "ERROR: Anthropic API key is not set. Please set ai-auto-complete-anthropic-api-key to use this backend."))
    (let* ((url "https://api.anthropic.com/v1/messages")
           (headers `(("Content-Type" . "application/json")
                     ("X-API-Key" . ,ai-auto-complete-anthropic-api-key)
                     ("Anthropic-Version" . "2023-06-01")))
           ;; Model attributes (temperature, max-tokens, etc.)
           (config-attrs (ai-auto-complete-apply-model-attributes 'anthropic model (make-hash-table :test 'equal)))
           (temperature (or (gethash "temperature" config-attrs) 0.7))
           (max-tokens (or (gethash "max_tokens" config-attrs) 1024))

           ;; Convert history to Anthropic format directly
           (anthropic-messages '())
           ;; Determine if native function calling should be used
           (use-native-tools (and (boundp 'ai-auto-complete-tools-enabled)
                                  ai-auto-complete-tools-enabled
                                  (fboundp 'ai-auto-complete-get-agent-tools)
                                  agent-name)))

      ;; Process history messages (reverse to get chronological order)
      (dolist (msg (reverse history))
        (pcase msg
          ;; User message
          (`(user . ,content)
           (push `((role . "user") (content . ,content)) anthropic-messages))

          ;; Agent/Assistant message - check if it contains tool calls
          (`(agent . (,name . ,content))
           (if (and use-native-tools (string-match-p "<tool name=" content))
               ;; Agent message contains tool calls - convert to assistant role with tool_use blocks
               (let ((tool-calls (ai-auto-complete-tools-parse-response content))
                     (text-without-tools (ai-auto-complete-tools-get-response-without-tools content)))
                 (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                   (message "[ANTHROPIC-DEBUG] Converting agent message with %d tool calls to tool_use format" (length tool-calls)))

                 ;; Create content array with text (if any) and tool uses
                 (let ((content-blocks '()))
                   ;; Add text block if there's content without tools
                   (when (and text-without-tools (not (string-empty-p (string-trim text-without-tools))))
                     (push `((type . "text") (text . ,text-without-tools)) content-blocks))

                   ;; Add tool_use blocks
                   (dolist (tool-call tool-calls)
                     (let* ((tool-name (car tool-call))
                            (tool-params (cdr tool-call))
                            ;; Convert parameters alist to JSON object for Anthropic
                            (input-object (if tool-params
                                            (let ((json-obj '()))
                                              (dolist (param tool-params)
                                                (push (cons (car param) (cdr param)) json-obj))
                                              json-obj)
                                          '()))
                            (tool-use-id (format "toolu_%s_%d" tool-name (random 10000))))
                       (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                         (message "[ANTHROPIC-DEBUG] Creating tool_use: name=%s, id=%s, input=%s" tool-name tool-use-id (json-encode input-object)))
                       (push `((type . "tool_use")
                              (id . ,tool-use-id)
                              (name . ,tool-name)
                              (input . ,input-object))
                             content-blocks)))

                   ;; Create the assistant message with tool uses
                   (push `((role . "assistant") (content . ,(vconcat (nreverse content-blocks)))) anthropic-messages)))
             ;; Regular agent message without tool calls - use assistant role
             (push `((role . "assistant") (content . ,content)) anthropic-messages)))

          ;; Tool result - handle both native function calling and XML formats
          (`(tool-result . ,content)
           (if (and use-native-tools (ai-auto-complete-anthropic--is-structured-tool-result content))
               ;; Native function calling: parse structured tool results into Anthropic tool_result content blocks
               (let ((anthropic-tool-result-blocks (ai-auto-complete-anthropic--parse-structured-tool-results content)))
                 (dolist (anthropic-block anthropic-tool-result-blocks) ; Each block is a complete tool_result content block
                   (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                     (message "[ANTHROPIC-DEBUG] Adding tool_result content block: %s" (json-encode anthropic-block)))
                   (push `((role . "user") ; Tool results are sent with role "user"
                           (content . [,anthropic-block])) ; Content is a list containing the single tool_result block
                         anthropic-messages)))
             ;; Fallback: Non-structured tool result (e.g., simple XML or plain text from older system)
             (let ((fallback-tool-use-id (format "toolu_fallback_%d" (random 100000))) ; Placeholder ID
                   (fallback-content-string (if (string-match "<tool_result name=\"[^\"]+\">\\(.*\\)</tool_result>" content)
                                                (ai-auto-complete-anthropic--trim (match-string 1 content))
                                              content)))
               (push `((role . "user")
                       (content . [((type . "tool_result")
                                   (tool_use_id . ,fallback-tool-use-id)
                                   (content . ,fallback-content-string))]))
                     anthropic-messages))))

          ;; Unknown format
          (_ (message "Warning: Unknown history format: %S" msg))))



      ;; Reverse to get correct chronological order
      (setq anthropic-messages (nreverse anthropic-messages))
      (message "DEBUG-SIMPLE: Converted %d history messages to %d Anthropic messages" (length history) (length anthropic-messages))

      ;; Debug the final messages
      (message "DEBUG-SIMPLE: Final Anthropic messages: %s" (json-encode anthropic-messages))

      (let (;; Prepare native function calling tools if tools are enabled
           (anthropic-native-tools nil)
           (payload `((model . ,model)
                      (system . ,system-prompt)
                      (messages . ,(vconcat anthropic-messages))
                      (temperature . ,temperature)
                      (max_tokens . ,max-tokens))))

      ;; Add native function calling tools if enabled AND no tool results are being sent in this turn
      (let ((contains-tool-result-p
             (cl-some (lambda (message-item)
                        (and (equal (cdr (assoc 'role message-item)) "user")
                             (let ((content (cdr (assoc 'content message-item))))
                               (and (vectorp content)
                                    (> (length content) 0)
                                    (equal (cdr (assoc 'type (aref content 0))) "tool_result")))))
                      anthropic-messages)))
        (when (and use-native-tools (not contains-tool-result-p))
          (let ((allowed-tools (ai-auto-complete-get-agent-tools agent-name)))
            (when allowed-tools
              (setq anthropic-native-tools (ai-auto-complete-anthropic--convert-tools-to-native-format allowed-tools))
              (when anthropic-native-tools
                (message "DEBUG-ANTHROPIC-TOOLS: Adding %d native tools for agent %s (no tool result in this turn)"
                         (length anthropic-native-tools) agent-name)
                (setq payload (append payload `((tools . ,(vconcat anthropic-native-tools)))))))))

      (let ((data (json-encode payload)))
        (message "Anthropic request payload (condensed): %s"
                 (substring data 0 (min 200 (length data))))
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[ANTHROPIC-DEBUG] Payload includes tools: %s" (not contains-tool-result-p)))
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[ANTHROPIC-DEBUG] Full payload size: %d characters" (length data))
          (let ((tool-result-count (cl-count-if (lambda (message)
                                                  (and (equal (cdr (assoc 'role message)) "user")
                                                       (let ((content (cdr (assoc 'content message))))
                                                         (and (vectorp content)
                                                              (> (length content) 0)
                                                              (equal (cdr (assoc 'type (aref content 0))) "tool_result")))))
                                                anthropic-messages)))
            (when (> tool-result-count 0)
              (message "[ANTHROPIC-DEBUG] Payload contains %d tool result messages" tool-result-count))))

        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "DEBUG-ANTHROPIC-REQUEST: Full payload structure:")
          (message "  - Messages count: %d" (length anthropic-messages))
          (message "  - Has system prompt: %s" (if system-prompt "yes" "no"))
          (message "  - Has native tools: %s" (if anthropic-native-tools "yes" "no"))
          (message "  - Payload keys: %s" (mapcar #'car payload))
          (let ((tool-result-messages (cl-count-if (lambda (message)
                                                     (and (equal (cdr (assoc 'role message)) "user")
                                                          (let ((content (cdr (assoc 'content message))))
                                                            (and (vectorp content)
                                                                 (> (length content) 0)
                                                                 (equal (cdr (assoc 'type (aref content 0))) "tool_result")))))
                                                   anthropic-messages)))
            (when (> tool-result-messages 0)
              (message "DEBUG-ANTHROPIC-REQUEST: Found %d tool result messages" tool-result-messages))))
              
        (setq ai-auto-complete--pending-request t)
        (ai-auto-complete--update-mode-line)
        (request url :type "POST" :headers headers
                 :data data :parser 'json-read
               :success (lambda (&rest args)
                         (let* ((response-data (plist-get args :data)))
                           ;; Check if the response contains an error
                           (if (assoc 'error response-data)
                               (let* ((error-obj (cdr (assoc 'error response-data)))
                                      (error-msg (cdr (assoc 'message error-obj))))
                                 (message "Anthropic API error message: %s" error-msg)
                                 (funcall callback (format "ERROR: %s" error-msg)))
                             ;; Normal response processing
                             (let* ((content-array (cdr (assoc 'content response-data)))
                                    (processed-parts '()) ; Will store text and XML tool strings, reversed at the end
                                    (found-any-tool-call-in-parts nil)) ; Flag if any part resulted in an XML tool

                               ;; Collect all parts, converting tool_use and tool_code to XML, preserving order
                               (when content-array
                                 (dotimes (i (length content-array))
                                   (let* ((part (aref content-array i))
                                          (part-type (cdr (assoc 'type part))))
                                     (cond
                                      ;; Case 1: Native tool_use (converted to XML for internal processing)
                                      ((string= part-type "tool_use")
                                       (let* ((tool-id (cdr (assoc 'id part))) ; Capture the tool_use_id from Anthropic
                                              (tool-name (cdr (assoc 'name part)))
                                              (tool-input (cdr (assoc 'input part)))
                                              (tool-args-json (json-encode tool-input))
                                              (xml-tool-call (format "<tool name=\"%s\" id=\"%s\"><parameters>%s</parameters></tool>" ; Embed id
                                                                     tool-name tool-id tool-args-json)))
                                         (push xml-tool-call processed-parts)
                                         (setq found-any-tool-call-in-parts t)))

                                      ;; Case 2: Text part (may contain tool_code, or just be text)
                                      ((string= part-type "text")
                                       (let ((text-data (cdr (assoc 'text part))))
                                         (if (and text-data (string-match "```tool_code\nprint(default_api\\.\\([a-zA-Z_][a-zA-Z0-9_]*\\)(\\(.*\\)))\n```" text-data))
                                             (let* ((tool_code-tool-name (match-string 1 text-data))
                                                    (tool_code-raw-args (match-string 2 text-data))
                                                    (tool_code-parsed-args-alist (ai-auto-complete-anthropic-parse-tool-code-args tool_code-raw-args))
                                                    (tool_code-args-json (json-encode tool_code-parsed-args-alist))
                                                    (xml-tool-call (format "<tool name=\"%s\"><parameters>%s</parameters></tool>"
                                                                           tool_code-tool-name tool_code-args-json)))
                                               (message "[ANTHROPIC-DEBUG] Parsed tool_code from text: %s with args %s" tool_code-tool-name tool_code-args-json)
                                               (push xml-tool-call processed-parts)
                                               (setq found-any-tool-call-in-parts t))
                                           ;; Else (text-data does not contain tool_code, or text-data is nil)
                                           (when text-data (push text-data processed-parts)))))))))

                               (setq processed-parts (nreverse processed-parts))

                               ;; Now, decide how to proceed
                               (if found-any-tool-call-in-parts
                                   ;; If any part resulted in a tool call (native tool_use or tool_code from text)
                                   ;; Pass the combined string of all parts (text and XML tools)
                                   (let ((combined-response-string (string-join processed-parts "\n")))
                                     (message "Anthropic provider response contains tool calls, processing combined string: %s"
                                              (substring combined-response-string 0 (min 200 (length combined-response-string))))
                                     (ai-auto-complete-tools-process-response combined-response-string callback agent-name))
                                 ;; ELSE: No tool_use or tool_code blocks found.
                                 ;; All parts in processed-parts are purely text segments.
                                 (let ((final-response-text (if processed-parts
                                                                (string-join processed-parts "\n")
                                                              "")))
                                   (if (and (boundp 'ai-auto-complete-tools-enabled)
                                            ai-auto-complete-tools-enabled
                                            (fboundp 'ai-auto-complete-tools-process-response)
                                            ;; Check if the *remaining text* contains manually inserted XML tool calls
                                            (string-match-p "<tool name=" final-response-text))
                                       (progn
                                         (message "Processing Anthropic text response for manually inserted XML tools with agent-name: %s" (or agent-name "nil"))
                                         (ai-auto-complete-tools-process-response final-response-text callback agent-name))
                                     ;; Else, it's a plain text response
                                     (funcall callback final-response-text))))))))
               :error (lambda (&rest args)
                       (let* ((request-error (plist-get args :error))
                              (response (plist-get args :response))
                              (error-data (plist-get args :data)))
                          (message "Error in Anthropic provider - Full error details:")
                          (message "Error: %S" request-error)
                          (message "Response: %S" response)
                          (message "Error Data: %S" error-data)
                          (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                            (message "DEBUG-ANTHROPIC-ERROR: Request contained %d messages" (length anthropic-messages))
                            (message "DEBUG-ANTHROPIC-ERROR: Request payload size: %d characters" (length data))
                            (let ((tool-result-messages (cl-count-if (lambda (message)
                                                                        (and (equal (cdr (assoc 'role message)) "user")
                                                                             (let ((content (cdr (assoc 'content message))))
                                                                               (and (vectorp content)
                                                                                    (> (length content) 0)
                                                                                    (equal (cdr (assoc 'type (aref content 0))) "tool_result")))))
                                                                      anthropic-messages)))
                              (when (> tool-result-messages 0)
                                (message "DEBUG-ANTHROPIC-ERROR: This request contained %d tool result messages" tool-result-messages))))
                          (funcall callback (format "ERROR: %s\nResponse: %S\nData: %S"
                                                  request-error response error-data))))
                 )))))))

;; Test functions for Anthropic API

(defun ai-auto-complete-check-anthropic-api-key ()
  "Check if the Anthropic API key is valid by making a simple test request."
  (interactive)
  (message "Checking Anthropic API key...")
  (if (string-empty-p ai-auto-complete-anthropic-api-key)
      (message "ERROR: Anthropic API key is not set")
    (let ((url "https://api.anthropic.com/v1/messages")
          (headers `(("Content-Type" . "application/json")
                    ("X-API-Key" . ,ai-auto-complete-anthropic-api-key)
                    ("Anthropic-Version" . "2023-06-01")))
          (data (json-encode `((model . "claude-3-haiku-20240307")
                              (system . "You are a helpful AI assistant.")
                              (messages . [((role . "user")
                                          (content . "What is the capital of France?"))])
                              (temperature . 0.7)
                              (max_tokens . 100)))))
      (message "Testing URL: %s" url)
      (request url
               :type "POST"
               :headers headers
               :data data
               :parser 'json-read
               :success (cl-function
                         (lambda (&key data &allow-other-keys)
                           (message "Anthropic API key is valid!")
                           (message "Test response received successfully")))
               :error (cl-function
                       (lambda (&key error-thrown response &allow-other-keys)
                         (message "Anthropic API key validation ERROR: %S" error-thrown)
                         (when response
                           (message "Response status: %s" (request-response-status-code response))
                           (message "Response data: %s" (request-response-data response)))
                         (message "Your Anthropic API key appears to be invalid or has insufficient permissions.")))))))

(defun ai-auto-complete-test-anthropic-native-tools-standalone ()
  "Test Anthropic native function calling conversion with mock data."
  (interactive)
  (message "Testing Anthropic native function calling conversion...")

  ;; Create mock tool definitions to test conversion
  (let ((mock-tools (make-hash-table :test 'equal)))
    ;; Add mock read_file tool
    (puthash "read_file"
             (list :description "Reads the entire content of a specified file and returns it as a string"
                   :function nil  ; Not needed for conversion test
                   :parameters '(("path" . "The absolute or relative path to the file whose content is to be read")))
             mock-tools)

    ;; Add mock write_file tool
    (puthash "write_file"
             (list :description "Writes the provided content to a specified file"
                   :function nil  ; Not needed for conversion test
                   :parameters '(("path" . "The absolute or relative path to the file where the content will be written")
                                ("content" . "The textual content to be written into the file")))
             mock-tools)

    ;; Temporarily replace the global tools hash table for testing
    (let ((original-tools ai-auto-complete-tools))
      (setq ai-auto-complete-tools mock-tools)

      ;; Test the conversion
      (let ((converted-tools (ai-auto-complete-anthropic--convert-tools-to-native-format '("read_file" "write_file"))))
        (message "Converted %d tools to Anthropic native format:" (length converted-tools))
        (dolist (tool converted-tools)
          (message "Tool: %s" (json-encode tool))))

      ;; Restore original tools
      (setq ai-auto-complete-tools original-tools))))

;; Register the provider
(ai-auto-complete-register-provider 'anthropic #'ai-auto-complete-anthropic-provider)

(provide 'providers/anthropic)
;;; anthropic.el ends here
