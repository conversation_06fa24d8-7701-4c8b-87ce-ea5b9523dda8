;;; logging-integration.el --- Integration of logging with ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file integrates the logging system with the AI Auto Complete package.
;; It adds advice to key functions to log LLM requests, responses, tool calls, etc.

;;; Code:

(require 'logging/logging-core)
(require 'core/backend)
(require 'tools/tools-core)
(require 'agents/agents-core)
(require 'cl-lib)
(require 'advice)

;; Advice for ai-auto-complete-complete to log LLM requests
(defun ai-auto-complete-logging-advice-complete (orig-fun backend context history callback &optional agent-name)
  "Advice function to log LLM requests.
Calls ORIG-FUN with BACKEND, CONTEXT, HISTORY, CALLBACK, and optional AGENT-NAME."
  ;; Start a new session if none is active
  (unless ai-auto-complete-logging-current-session-id
    (ai-auto-complete-logging-start-session))

  ;; Log the request
  (let* ((effective-backend (if (and agent-name
                                    (boundp 'ai-auto-complete-use-agent-specific-models)
                                    ai-auto-complete-use-agent-specific-models
                                    (fboundp 'ai-auto-complete-get-agent-default-model))
                               (let ((agent-model (ai-auto-complete-get-agent-default-model agent-name)))
                                 (if (and agent-model (plist-get agent-model :backend))
                                     (plist-get agent-model :backend)
                                   backend))
                             backend))
         (model-tag (if (and agent-name
                            (boundp 'ai-auto-complete-use-agent-specific-models)
                            ai-auto-complete-use-agent-specific-models
                            (fboundp 'ai-auto-complete-get-agent-default-model))
                       (let ((agent-model (ai-auto-complete-get-agent-default-model agent-name)))
                         (if (and agent-model (plist-get agent-model :tag))
                             (plist-get agent-model :tag)
                           (ai-auto-complete-get-current-model effective-backend)))
                     (ai-auto-complete-get-current-model effective-backend)))
         (model-name (when (fboundp 'ai-auto-complete-get-correct-model-name)
                       (ai-auto-complete-get-correct-model-name effective-backend model-tag))))

    (ai-auto-complete-logging-log-llm-request
     effective-backend
     (or model-name model-tag)
     context
     agent-name
     history))

  ;; Create a wrapped callback to log the response
  (let ((wrapped-callback
         (lambda (response)
           ;; Log the response
           (let* ((effective-backend (if (and agent-name
                                             (boundp 'ai-auto-complete-use-agent-specific-models)
                                             ai-auto-complete-use-agent-specific-models
                                             (fboundp 'ai-auto-complete-get-agent-default-model))
                                        (let ((agent-model (ai-auto-complete-get-agent-default-model agent-name)))
                                          (if (and agent-model (plist-get agent-model :backend))
                                              (plist-get agent-model :backend)
                                            backend))
                                      backend))
                  (model-tag (if (and agent-name
                                     (boundp 'ai-auto-complete-use-agent-specific-models)
                                     ai-auto-complete-use-agent-specific-models
                                     (fboundp 'ai-auto-complete-get-agent-default-model))
                                (let ((agent-model (ai-auto-complete-get-agent-default-model agent-name)))
                                  (if (and agent-model (plist-get agent-model :tag))
                                      (plist-get agent-model :tag)
                                    (ai-auto-complete-get-current-model effective-backend)))
                              (ai-auto-complete-get-current-model effective-backend)))
                  (model-name (when (fboundp 'ai-auto-complete-get-correct-model-name)
                                (ai-auto-complete-get-correct-model-name effective-backend model-tag)))
                  (contains-tool-calls (and (boundp 'ai-auto-complete-tools-enabled)
                                           ai-auto-complete-tools-enabled
                                           (string-match-p "<tool name=" response))))

             (ai-auto-complete-logging-log-llm-response
              effective-backend
              (or model-name model-tag)
              response
              agent-name
              contains-tool-calls))

           ;; Call the original callback
           (funcall callback response))))

    ;; Call the original function with our wrapped callback
    (funcall orig-fun backend context history wrapped-callback agent-name)))

;; Advice for ai-auto-complete-tools-process-response to log tool calls
(defun ai-auto-complete-logging-advice-process-response (orig-fun response callback agent-name)
  "Advice function to log tool calls in responses.
Calls ORIG-FUN with RESPONSE, CALLBACK, and optional AGENT-NAME."
  ;; Check if there are any tool calls in the response
  (if (and (boundp 'ai-auto-complete-tools-enabled)
           ai-auto-complete-tools-enabled
           (string-match-p "<tool name=" response))
      (let ((tool-calls (ai-auto-complete-tools-parse-response response)))
        ;; Log each tool call
        (dolist (tool-call tool-calls)
          (let ((tool-name (nth 0 tool-call))      ; First element: tool name
                (params (nth 1 tool-call))         ; Second element: parameters
                (tool-id (nth 2 tool-call)))       ; Third element: tool-id (may be nil)
            (ai-auto-complete-logging-log-tool-call tool-name params agent-name)
            ;; Log tool-id if present
            (when tool-id
              (ai-auto-complete-logging-log-message
               (format "Tool call ID: %s" tool-id) 'tool-id agent-name))))

        ;; Create a wrapped callback to log tool results
        (let ((wrapped-callback
               (lambda (result)
                 ;; Log the result (this is a simplification, as we don't have the individual tool results)
                 (ai-auto-complete-logging-log
                  'info
                  agent-name
                  "Tool processing completed"
                  `((result . ,result)))

                 ;; Call the original callback
                 (funcall callback result))))

          ;; Call the original function with our wrapped callback
          (funcall orig-fun response wrapped-callback agent-name)))

    ;; No tool calls, just call the original function
    (funcall orig-fun response callback agent-name)))

;; Advice for ai-auto-complete-tools-execute-tool to log individual tool executions
(defun ai-auto-complete-logging-advice-execute-tool (orig-fun tool-name params callback)
  "Advice function to log individual tool executions.
Calls ORIG-FUN with TOOL-NAME, PARAMS, and CALLBACK."
  ;; Create a wrapped callback to log the tool result
  (let ((wrapped-callback
         (lambda (result)
           ;; Log the tool result
           (ai-auto-complete-logging-log-tool-result
            tool-name
            result
            nil  ;; We don't have agent-name here
            t)   ;; Assume success

           ;; Call the original callback
           (funcall callback result))))

    ;; Call the original function with our wrapped callback
    (funcall orig-fun tool-name params wrapped-callback)))

;; Advice for ai-auto-complete-process-agent-message to log agent interactions
(defun ai-auto-complete-logging-advice-process-agent-message (orig-fun agent-name message history callback)
  "Advice function to log agent interactions.
Calls ORIG-FUN with AGENT-NAME, MESSAGE, HISTORY, and CALLBACK."
  (message "[DEBUG-LOGGING] ai-auto-complete-logging-advice-process-agent-message: agent-name=%S, message=%S, history-length=%d"
           agent-name message (length history))

  ;; Start a new session if none is active
  (unless ai-auto-complete-logging-current-session-id
    (ai-auto-complete-logging-start-session))

  ;; Log the agent message
  (ai-auto-complete-logging-log
   'info
   agent-name
   "Agent message"
   `((message . ,message)
     (history_length . ,(length history))))

  ;; Create a wrapped callback to log the agent response
  (let ((wrapped-callback
         (lambda (agent response)
           (message "[DEBUG-LOGGING] ai-auto-complete-logging-advice-process-agent-message (callback): agent=%S, response=%S"
                    agent response)
           ;; Log the agent response
           (ai-auto-complete-logging-log
            'info
            agent
            "Agent response"
            `((response . ,response)))

           ;; Call the original callback
           (funcall callback agent response))))

    ;; Call the original function with our wrapped callback
    (funcall orig-fun agent-name message history wrapped-callback)))

;; Advice for OpenAI provider to log raw requests and responses
(defun ai-auto-complete-logging-advice-openai-provider (orig-fun history callback model system-prompt &optional agent-name)
  "Advice function to log raw OpenAI requests and responses.
Calls ORIG-FUN with HISTORY, CALLBACK, MODEL, SYSTEM-PROMPT, and optional AGENT-NAME."
  (message "[DEBUG-LOGGING] OpenAI provider advice triggered: model=%S, agent-name=%S, detailed-enabled=%S"
           model agent-name ai-auto-complete-logging-detailed-enabled)

  ;; Start a new session if none is active
  (unless ai-auto-complete-logging-current-session-id
    (ai-auto-complete-logging-start-session))

  ;; We need to intercept the request call to log the raw data
  ;; This is a bit complex because we need to wrap the request function
  (let ((original-request (symbol-function 'request)))
    (cl-letf (((symbol-function 'request)
               (lambda (url &rest args)
                 ;; Extract the data from the request
                 (let ((data (plist-get args :data))
                       (headers (plist-get args :headers))
                       (success-callback (plist-get args :success)))

                   (message "[DEBUG-LOGGING] OpenAI request: url=%S, headers=%S, data=%S"
                            url headers data)

                   ;; Log the raw request
                   (when data
                     (ai-auto-complete-logging-log-llm-request-raw
                      'openai model url headers data agent-name))

                   ;; Wrap the success callback to log the raw response
                   (when success-callback
                     (setq args (plist-put args :success
                                          (lambda (&rest success-args)
                                            (let ((response-data (plist-get success-args :data)))
                                              (message "[DEBUG-LOGGING] OpenAI response: url=%S, response-data=%S"
                                                       url response-data)
                                              ;; Log the raw response
                                              (when response-data
                                                (ai-auto-complete-logging-log-llm-response-raw
                                                 'openai model url response-data agent-name))
                                              ;; Call the original success callback
                                              (apply success-callback success-args))))))

                   ;; Call the original request function
                   (apply original-request url args)))))

      ;; Call the original provider function
      (funcall orig-fun history callback model system-prompt agent-name))))

;; Advice for Anthropic provider to log raw requests and responses
(defun ai-auto-complete-logging-advice-anthropic-provider (orig-fun history callback model system-prompt &optional agent-name)
  "Advice function to log raw Anthropic requests and responses.
Calls ORIG-FUN with HISTORY, CALLBACK, MODEL, SYSTEM-PROMPT, and optional AGENT-NAME."
  (message "[DEBUG-LOGGING] Anthropic provider advice triggered: model=%S, agent-name=%S, detailed-enabled=%S"
           model agent-name ai-auto-complete-logging-detailed-enabled)

  ;; Start a new session if none is active
  (unless ai-auto-complete-logging-current-session-id
    (ai-auto-complete-logging-start-session))

  ;; We need to intercept the request call to log the raw data
  (let ((original-request (symbol-function 'request)))
    (cl-letf (((symbol-function 'request)
               (lambda (url &rest args)
                 ;; Extract the data from the request
                 (let ((data (plist-get args :data))
                       (headers (plist-get args :headers))
                       (success-callback (plist-get args :success)))

                   (message "[DEBUG-LOGGING] Anthropic request: url=%S, headers=%S, data=%S"
                            url headers data)

                   ;; Log the raw request
                   (when data
                     (ai-auto-complete-logging-log-llm-request-raw
                      'anthropic model url headers data agent-name))

                   ;; Wrap the success callback to log the raw response
                   (when success-callback
                     (setq args (plist-put args :success
                                          (lambda (&rest success-args)
                                            (let ((response-data (plist-get success-args :data)))
                                              (message "[DEBUG-LOGGING] Anthropic response: url=%S, response-data=%S"
                                                       url response-data)
                                              ;; Log the raw response
                                              (when response-data
                                                (ai-auto-complete-logging-log-llm-response-raw
                                                 'anthropic model url response-data agent-name))
                                              ;; Call the original success callback
                                              (apply success-callback success-args))))))

                   ;; Call the original request function
                   (apply original-request url args)))))

      ;; Call the original provider function
      (funcall orig-fun history callback model system-prompt agent-name))))

;; Advice for Gemini provider to log raw requests and responses
(defun ai-auto-complete-logging-advice-gemini-provider (orig-fun history callback model system-prompt &optional agent-name)
  "Advice function to log raw Gemini requests and responses.
Calls ORIG-FUN with HISTORY, CALLBACK, MODEL, SYSTEM-PROMPT, and optional AGENT-NAME."
  (message "[DEBUG-LOGGING] ai-auto-complete-logging-advice-gemini-provider: model=%S, agent-name=%S"
           model agent-name)

  ;; Start a new session if none is active
  (unless ai-auto-complete-logging-current-session-id
    (ai-auto-complete-logging-start-session))

  ;; We need to intercept the request call to log the raw data
  (let ((original-request (symbol-function 'request)))
    (cl-letf (((symbol-function 'request)
               (lambda (url &rest args)
                 ;; Extract the data from the request
                 (let ((data (plist-get args :data))
                       (headers (plist-get args :headers))
                       (success-callback (plist-get args :success)))

                   (message "[DEBUG-LOGGING] Gemini request: url=%S, headers=%S, data=%S"
                            url headers data)

                   ;; Log the raw request
                   (when data
                     (ai-auto-complete-logging-log-llm-request-raw
                      'gemini model url headers data agent-name))

                   ;; Wrap the success callback to log the raw response
                   (when success-callback
                     (setq args (plist-put args :success
                                          (lambda (&rest success-args)
                                            (let ((response-data (plist-get success-args :data)))
                                              (message "[DEBUG-LOGGING] Gemini response: url=%S, response-data=%S"
                                                       url response-data)
                                              ;; Log the raw response
                                              (when response-data
                                                (ai-auto-complete-logging-log-llm-response-raw
                                                 'gemini model url response-data agent-name))
                                              ;; Call the original success callback
                                              (apply success-callback success-args))))))

                   ;; Call the original request function
                   (apply original-request url args)))))

      ;; Call the original provider function
      (funcall orig-fun history callback model system-prompt agent-name))))

;; Advice for OpenRouter provider to log raw requests and responses
(defun ai-auto-complete-logging-advice-openrouter-provider (orig-fun history callback model system-prompt &optional agent-name)
  "Advice function to log raw OpenRouter requests and responses.
Calls ORIG-FUN with HISTORY, CALLBACK, MODEL, SYSTEM-PROMPT, and optional AGENT-NAME."
  (message "[DEBUG-LOGGING] ai-auto-complete-logging-advice-openrouter-provider: model=%S, agent-name=%S"
           model agent-name)

  ;; Start a new session if none is active
  (unless ai-auto-complete-logging-current-session-id
    (ai-auto-complete-logging-start-session))

  ;; We need to intercept the request call to log the raw data
  (let ((original-request (symbol-function 'request)))
    (cl-letf (((symbol-function 'request)
               (lambda (url &rest args)
                 ;; Extract the data from the request
                 (let ((data (plist-get args :data))
                       (headers (plist-get args :headers))
                       (success-callback (plist-get args :success)))

                   (message "[DEBUG-LOGGING] OpenRouter request: url=%S, headers=%S, data=%S"
                            url headers data)

                   ;; Log the raw request
                   (when data
                     (ai-auto-complete-logging-log-llm-request-raw
                      'openrouter model url headers data agent-name))

                   ;; Wrap the success callback to log the raw response
                   (when success-callback
                     (setq args (plist-put args :success
                                          (lambda (&rest success-args)
                                            (let ((response-data (plist-get success-args :data)))
                                              (message "[DEBUG-LOGGING] OpenRouter response: url=%S, response-data=%S"
                                                       url response-data)
                                              ;; Log the raw response
                                              (when response-data
                                                (ai-auto-complete-logging-log-llm-response-raw
                                                 'openrouter model url response-data agent-name))
                                              ;; Call the original success callback
                                              (apply success-callback success-args))))))

                   ;; Call the original request function
                   (apply original-request url args)))))

      ;; Call the original provider function
      (funcall orig-fun history callback model system-prompt agent-name))))

;; Install advice
(defun ai-auto-complete-logging-install-advice ()
  "Install advice functions for logging."
  (interactive)

  ;; Advice for backend completion
 ; (advice-add 'ai-auto-complete-complete :around
    ;          #'ai-auto-complete-logging-advice-complete '(:depth 0))

  ;; Advice for tool processing
  ;(advice-add 'ai-auto-complete-tools-process-response :around
     ;         #'ai-auto-complete-logging-advice-process-response '(:depth 0))

  ;; Advice for tool execution
  ;(when (fboundp 'ai-auto-complete-tools-execute-tool)
  ;  (advice-add 'ai-auto-complete-tools-execute-tool :around
  ;             #'ai-auto-complete-logging-advice-execute-tool))

  ;; Advice for agent message processing
  ;(advice-add 'ai-auto-complete-process-agent-message :around
  ;           #'ai-auto-complete-logging-advice-process-agent-message)

  ;; Advice for provider functions to log raw requests/responses
  (when ai-auto-complete-logging-detailed-enabled
    (when (fboundp 'ai-auto-complete-openai-provider)
      (advice-add 'ai-auto-complete-openai-provider :around
                  #'ai-auto-complete-logging-advice-openai-provider))

    (when (fboundp 'ai-auto-complete-anthropic-provider)
      (advice-add 'ai-auto-complete-anthropic-provider :around
                  #'ai-auto-complete-logging-advice-anthropic-provider))

    (when (fboundp 'ai-auto-complete-gemini-provider)
      (advice-add 'ai-auto-complete-gemini-provider :around
                  #'ai-auto-complete-logging-advice-gemini-provider))

    (when (fboundp 'ai-auto-complete-openrouter-provider)
      (advice-add 'ai-auto-complete-openrouter-provider :around
                  #'ai-auto-complete-logging-advice-openrouter-provider)))

  (message "AI Auto Complete logging advice installed"))

;; Remove advice
(defun ai-auto-complete-logging-remove-advice ()
  "Remove advice functions for logging."
  (interactive)

  ;; Remove advice for backend completion
  (advice-remove 'ai-auto-complete-complete
                #'ai-auto-complete-logging-advice-complete)

  ;; Remove advice for tool processing
  (advice-remove 'ai-auto-complete-tools-process-response
                #'ai-auto-complete-logging-advice-process-response)

  ;; Remove advice for tool execution
  (when (fboundp 'ai-auto-complete-tools-execute-tool)
    (advice-remove 'ai-auto-complete-tools-execute-tool
                  #'ai-auto-complete-logging-advice-execute-tool))

  ;; Remove advice for agent message processing
  (advice-remove 'ai-auto-complete-process-agent-message
                #'ai-auto-complete-logging-advice-process-agent-message)

  ;; Remove advice for provider functions
  (when (fboundp 'ai-auto-complete-openai-provider)
    (advice-remove 'ai-auto-complete-openai-provider
                  #'ai-auto-complete-logging-advice-openai-provider))

  (when (fboundp 'ai-auto-complete-anthropic-provider)
    (advice-remove 'ai-auto-complete-anthropic-provider
                  #'ai-auto-complete-logging-advice-anthropic-provider))

  (when (fboundp 'ai-auto-complete-gemini-provider)
    (advice-remove 'ai-auto-complete-gemini-provider
                  #'ai-auto-complete-logging-advice-gemini-provider))

  (when (fboundp 'ai-auto-complete-openrouter-provider)
    (advice-remove 'ai-auto-complete-openrouter-provider
                  #'ai-auto-complete-logging-advice-openrouter-provider))

  (message "AI Auto Complete logging advice removed"))

;; Initialize logging integration
(defun ai-auto-complete-logging-integration-initialize ()
  "Initialize logging integration."
  (interactive)

  ;; Initialize the core logging system
  (ai-auto-complete-logging-initialize)

  ;; Install advice if logging is enabled
  (when ai-auto-complete-logging-enabled
    (ai-auto-complete-logging-install-advice))

  (message "AI Auto Complete logging integration initialized"))

(provide 'logging/logging-integration)
;;; logging-integration.el ends here
