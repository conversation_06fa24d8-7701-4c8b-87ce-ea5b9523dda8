;;; tools-core.el --- Core tool functionality for ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides core tool functionality for the AI Auto Complete package.
;; The state machine implementation is in tools-state-machine.el to avoid callback hell.

;;; Code:

(require 'cl-lib)
(require 'xml)
;; Ensure ai-auto-complete-chat-buffer-name is available.
;; This variable is expected to be defined in chat-customization.el
;; and typically holds "AI Auto Complete Chat".
(require 'chat-customization)
(require 'ui/enhanced-chat) ; For enhanced chat buffer name and mode

;; Handle missing dependencies gracefully
;(defvar ai-auto-complete-backend nil
 ; "Current AI backend being used.")

;(defun ai-auto-complete-get-current-backend ()
 ; "Get the current AI backend."
  ;(or ai-auto-complete-backend 'default-backend))

;(defun ai-auto-complete-complete (backend prompt history callback &rest _args)
 ; "Send PROMPT to BACKEND with HISTORY and call CALLBACK with the response.
;This is a fallback implementation when the real one isn't available.
;The &rest _args parameter handles optional arguments like agent-name."
;  (message "Warning: Using fallback ai-auto-complete-complete implementation")
;  (funcall callback (format "Error: Could not process request. The AI backend system is not properly loaded.\nPrompt: %s" prompt)))

;; Tool registry
(defvar ai-auto-complete-tools (make-hash-table :test 'equal)
  "Hash table of available tools, mapping tool names to functions.")

;; Enable/disable tools
(defcustom ai-auto-complete-tools-enabled nil
  "Whether to enable tool use in AI Auto Complete."
  :type 'boolean
  :group 'ai-auto-complete)


;; NB Note: which function gets called after this function gets called? Is a hook or advice attached to this function?
;; Apparently, after this function gets executed, stdio bridge gets called. with callback. Which callbacK? I think there is no
;; need for that callback after this tool gets registered because we already called the server, initialized it and registered the tool.
;; Register a tool
(defun ai-auto-complete-register-tool (name description function parameters)
  "Register a tool with NAME, DESCRIPTION, FUNCTION and PARAMETERS.
PARAMETERS should be an alist of parameter names and their descriptions."
  (puthash name (list :description description
                     :function function
                     :parameters parameters)
           ai-auto-complete-tools)
  (message "Registered tool: %s" name)

  ;; Add debug logging for MCP tools
  (when (and (boundp 'ai-auto-complete-mcp-debug-mode)
             ai-auto-complete-mcp-debug-mode
             (string-match "^mcp:" name))
    (message "[MCP-DEBUG] Registered MCP tool: %s" name)

    ;; Count total MCP tools
    (let ((mcp-tools 0))
      (maphash (lambda (tool-name _)
                 (when (string-match "^mcp:" tool-name)
                   (setq mcp-tools (1+ mcp-tools))))
               ai-auto-complete-tools)
      (message "[MCP-DEBUG] Total MCP tools registered: %d" mcp-tools))))

;; Helper function to format a single tool definition
(defun ai-auto-complete-format-tool-definition (name tool)
  "Format a single tool definition for NAME and TOOL."
  (let ((description (plist-get tool :description))
        (parameters (plist-get tool :parameters))
        (param-strings '()))

    ;; Format each parameter separately to avoid deep nesting
    (dolist (param parameters)
      (push (format "%s (%s)" (car param) (cdr param)) param-strings))

    ;; Join the parameter strings with commas
    (let ((param-text (mapconcat #'identity (nreverse param-strings) ", ")))
      (format "- %s: %s. Parameters: %s\n" name description param-text))))

;; Get tool definitions as a string
(defun ai-auto-complete-get-tool-definitions (&optional allowed-tools)
  "Get tool definitions as a formatted string for inclusion in prompts.
If ALLOWED-TOOLS is provided, only include tools in that list."
  (message "DEBUG-GET-TOOLS: Called with allowed-tools: %s"
           (if allowed-tools
               (format "a list with %d tools" (length allowed-tools))
             "nil"))

  ;; If allowed-tools is provided but empty, return empty tool definitions
  (if (and allowed-tools (= (length allowed-tools) 0))
      (progn
        (message "DEBUG-GET-TOOLS: Empty allowed-tools list provided, returning empty tool definitions")
        "Available tools:\nNo tools are available for this agent.\n")

    ;; Otherwise, proceed with normal tool definitions
    (let ((tools-text "Available tools:\n")
          (tool-count 0)
          (mcp-tool-count 0)
          (allowed-count 0)
          (filtered-count 0)
          (tool-names nil))

      ;; First, collect all tool names to avoid nested maphash
      ;; Avoid using lambda inside maphash to prevent excessive nesting
      (let ((keys nil))
        (maphash (lambda (k _v) (push k keys)) ai-auto-complete-tools)
        (setq tool-names keys))

      (setq allowed-count (length tool-names))

      ;; Process each tool
      (dolist (name tool-names)
        (let ((tool (gethash name ai-auto-complete-tools)))
          ;; Only include the tool if it's in the allowed-tools list or if allowed-tools is nil
          (when (or (null allowed-tools) (member name allowed-tools))
            (setq filtered-count (1+ filtered-count))
            (setq tool-count (1+ tool-count))
            (when (string-match "^mcp:" name)
              (setq mcp-tool-count (1+ mcp-tool-count)))

            ;; Format and add this tool definition
            (setq tools-text (concat tools-text (ai-auto-complete-format-tool-definition name tool))))))

      ;; Add debug logging for MCP tools
      (when (and (boundp 'ai-auto-complete-mcp-debug-mode) ai-auto-complete-mcp-debug-mode)
        (message "[MCP-DEBUG] Generated tool definitions with %d total tools (%d MCP tools)"
                 tool-count mcp-tool-count))

      (message "DEBUG-GET-TOOLS: Found %d total tools, included %d tools in output%s"
               allowed-count
               filtered-count
               (if allowed-tools
                   (format " (filtered by %d allowed tools)" (length allowed-tools))
                 " (no filtering)"))

      tools-text)))

;; Get tool instructions text
(defun ai-auto-complete-get-tool-instructions ()
  "Get the standard instructions for using tools."
  (concat
   "You have access to the following tools. When you need to use a tool, format your response using XML tags like this:\n"
   "<tool name=\"tool_name\">\n"
   "<parameters>\n"
   "{\"param1\": \"value1\", \"param2\": \"value2\"}\n"
   "</parameters>\n"
   "</tool>\n\n"
   "You can use multiple tools in a single response by including multiple tool tags:\n"
   "<tool name=\"tool_name_1\">\n"
   "<parameters>\n"
   "{\"param1\": \"value1\"}\n"
   "</parameters>\n"
   "</tool>\n\n"
   "<tool name=\"tool_name_2\">\n"
   "<parameters>\n"
   "{\"param2\": \"value2\"}\n"
   "</parameters>\n"
   "</tool>\n\n"
   "When you use a tool, I will execute it and return the results to you in this format:\n"
   "<tool_result name=\"tool_name\">\n"
   "Result data or error message\n"
   "</tool_result>\n\n"
   "If you used multiple tools, you will receive multiple tool results:\n"
   "<tool_result name=\"tool_name_1\">\n"
   "Result data from first tool\n"
   "</tool_result>\n\n"
   "<tool_result name=\"tool_name_2\">\n"
   "Result data from second tool\n"
   "</tool_result>\n\n"
   "After receiving tool results, you should analyze them and continue the conversation.\n"
   "You can use tools for two purposes:\n"
   "1. To get DATA: When you need information, I'll provide the data in the response\n"
   "2. To perform ACTIONS: When you want to make something happen, I'll tell you if it succeeded or failed\n\n"
   "Based on the tool results, you should decide what to do next. You can:\n"
   "- Continue the conversation normally if you have all the information you need\n"
   "- Use another tool if you need more information or need to perform another action\n"
   "- Explain any errors that occurred and suggest alternatives\n"
   "- Don't ask unnecessary questions, clarification from USER, rather use tool to accomplish the task yourself.\n\n"))

;; Add tool definitions to a prompt
(defun ai-auto-complete-tools-add-to-prompt (prompt &optional agent-name)
  "Add tool definitions to PROMPT.
If AGENT-NAME is provided, only include tools allowed for that agent."
  (message "Adding tool definitions to prompt")
  (message "DEBUG-TOOLS-ADD: Called with agent-name: %s" (or agent-name "nil"))

  ;; Early return if tools are not enabled
  (if (not ai-auto-complete-tools-enabled)
      (progn
        (message "Tools not enabled, returning original prompt")
        prompt)

    ;; Get allowed tools for the agent if applicable
    (let ((allowed-tools nil)
          (use-allowed-tools nil)
          (effective-allowed-tools nil))

      ;; Set up allowed tools
      (when (and agent-name (fboundp 'ai-auto-complete-get-agent-tools))
        (setq allowed-tools (funcall 'ai-auto-complete-get-agent-tools agent-name))
        (setq use-allowed-tools t))

      ;; Set effective allowed tools
      (setq effective-allowed-tools (if use-allowed-tools allowed-tools nil))

      ;; Debug logging
      (message "DEBUG-TOOLS-ADD: allowed-tools type: %s" (type-of allowed-tools))
      (message "DEBUG-TOOLS-ADD: effective-allowed-tools type: %s" (type-of effective-allowed-tools))
      (message "DEBUG-TOOLS-ADD: effective-allowed-tools value: %s"
               (if effective-allowed-tools
                   (format "%s" effective-allowed-tools)
                 "nil"))

      ;; Get tool definitions
      (let ((tool-definitions (ai-auto-complete-get-tool-definitions effective-allowed-tools)))
        (message "DEBUG-TOOLS-ADD: use-allowed-tools = %s" use-allowed-tools)

        ;; More debug logging
        (message "DEBUG-TOOLS-ADD: allowed-tools is %s"
                 (if allowed-tools
                     (format "a list with %d tools" (length allowed-tools))
                   "nil"))

        (when allowed-tools
          (message "Agent %s has %d allowed tools" agent-name (length allowed-tools)))

        ;; Special handling for empty allowed-tools list
        (when (and allowed-tools (= (length allowed-tools) 0))
          (message "DEBUG-TOOLS-ADD: Agent %s has an empty allowed-tools list, will include NO tools" agent-name))

        ;; Log tool definitions
        (message "Got tool definitions: %s"
                 (substring tool-definitions 0 (min 100 (length tool-definitions))))

        ;; Create modified prompt
        (let ((tool-instructions (ai-auto-complete-get-tool-instructions))
              (modified-prompt nil))

          ;; Combine all parts
          (setq modified-prompt (concat prompt "\n\n" tool-instructions tool-definitions))

          ;; Log result
          (message "Modified prompt with tool definitions")
          (message "DEBUG-TOOLS-ADD: Returning modified prompt with %s tools"
                   (if use-allowed-tools
                       (if allowed-tools
                           (format "only the %d allowed tools for agent %s" (length allowed-tools) agent-name)
                         (format "NO tools for agent %s (agent has no tools)" agent-name))
                     "all available tools (no agent-specific filtering)"))

          ;; Return the modified prompt
          modified-prompt)))))






;; Parse tool calls from a response
(defun ai-auto-complete-tools-parse-response (response)
  "Parse tool calls from RESPONSE and return a list of (tool-name parameters-alist tool-id).
This function is robust and can handle malformed XML tags by making reasonable assumptions.
The tool-id is extracted from the id attribute if present, otherwise nil."
  (let ((tool-calls nil)
        (start 0)
        (tool-count 0))
    ;; Look for tool tags in the response - updated regex to optionally capture id attribute
    (while (string-match "<tool name=\"\\([^\"]+\\)\"\\(?:\\s-+id=\"\\([^\"]+\\)\"\\)?>" response start)
      (setq tool-count (1+ tool-count))
      (let* ((tool-name (match-string 1 response))
             (tool-id (match-string 2 response))  ; Extract tool-id from id attribute (group 2)
             (tool-start (match-end 0))
             (params-start (string-match "<parameters>" response tool-start))
             (params-content-start (and params-start (+ params-start (length "<parameters>"))))
             (params-end (and params-content-start (string-match "</parameters>" response params-content-start)))
             (tool-end (string-match "</tool>" response tool-start))
             ;; Find the next tool start to determine boundaries - updated regex to match new pattern
             (next-tool-start (string-match "<tool name=\"[^\"]+\"\\(?:\\s-+id=\"[^\"]+\"\\)?>" response (1+ tool-start)))
             ;; Determine the actual end of this tool's content
             (content-end (or tool-end next-tool-start (length response))))

        ;; Debug logging
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[TOOLS-DEBUG] Found tool call #%d: %s (id: %s)" tool-count tool-name (or tool-id "none"))
          (when (not params-start)
            (message "[TOOLS-DEBUG] WARNING: Missing <parameters> tag for tool %s, will try to extract anyway" tool-name))
          (when (and params-start (not params-end))
            (message "[TOOLS-DEBUG] WARNING: Missing </parameters> tag for tool %s, will try to extract anyway" tool-name))
          (when (not tool-end)
            (message "[TOOLS-DEBUG] WARNING: Missing </tool> tag for tool %s, will try to extract anyway" tool-name)))

        ;; Try to extract parameters even if tags are malformed
        (let* ((params-content
                (cond
                 ;; Best case: both parameters tags are present
                 ((and params-content-start params-end)
                  (substring response params-content-start params-end))
                 ;; Missing closing tag: extract until tool end or next tool
                 ((and params-content-start (not params-end))
                  (let ((extract-end (or tool-end next-tool-start (length response))))
                    (substring response params-content-start extract-end)))
                 ;; Missing opening tag: try to find JSON-like content
                 ((not params-start)
                  (let ((tool-content (substring response tool-start content-end)))
                    ;; Look for JSON-like content (starts with { or [)
                    (when (string-match "\\s-*\\([{[].*\\)" tool-content)
                      (match-string 1 tool-content))))
                 ;; Fallback: empty parameters
                 (t "")))
               (params
                (when (and params-content (not (string-empty-p (string-trim params-content))))
                  (condition-case err
                      (json-read-from-string (string-trim params-content))
                    (error
                     (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                       (message "[TOOLS-DEBUG] ERROR: Failed to parse JSON parameters for tool %s: %s"
                                tool-name (error-message-string err))
                       (message "[TOOLS-DEBUG] Raw parameters content: %s" params-content))
                     ;; Try to extract simple key-value pairs as fallback
                     (ai-auto-complete-tools-parse-simple-params params-content))))))

          ;; Always add the tool call, now including tool-id as third element
          (push (list tool-name (or params '()) tool-id) tool-calls)

          (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
            (if params
                (message "[TOOLS-DEBUG] Successfully parsed parameters for tool %s (id: %s): %s"
                         tool-name (or tool-id "none") (prin1-to-string params))
              (message "[TOOLS-DEBUG] Using empty parameters for tool %s (id: %s)" tool-name (or tool-id "none")))))

        ;; Move to next position
        (setq start (or next-tool-start content-end))))

    (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
      (message "[TOOLS-DEBUG] Parsed %d tool calls from response" (length tool-calls)))

    (nreverse tool-calls)))


(defun ai-auto-complete-tools-parse-simple-params (content)
  "Fallback function to parse simple parameter formats when JSON parsing fails.
Tries to extract key-value pairs from various formats."
  (condition-case err
      (let ((params '()))
        ;; Try to find simple key: value patterns
        (when (string-match-p ":" content)
          (dolist (line (split-string content "\n"))
            (when (string-match "\\s-*\\([^:]+\\):\\s-*\\(.+\\)" line)
              (let ((key (string-trim (match-string 1 line)))
                    (value (string-trim (match-string 2 line))))
                ;; Remove quotes if present
                (when (and (string-prefix-p "\"" value) (string-suffix-p "\"" value))
                  (setq value (substring value 1 -1)))
                (push (cons (intern key) value) params)))))

        ;; If no key:value pairs found, try to find quoted strings as values
        (when (null params)
          (let ((strings (split-string content "\"" t)))
            (when (> (length strings) 1)
              ;; Use the first quoted string as a default parameter
              (push (cons 'content (nth 1 strings)) params))))

        params)
    (error nil)))

;; Default callback for presenting results to the user
;; function that gets called after processing the response
(defun ai-auto-complete-tools-default-callback (response &optional agent-name)
  "Default callback to present RESPONSE to the user in the chat interface.
This is used when no callback is provided but we still need to show results to the user.
AGENT-NAME is the optional name of the agent making the request.
The `ai-auto-complete-chat-mode` variable is buffer-local. This function
must ensure it's operating in the context of the correct chat buffer."
  (message "Default callback: Presenting response. Agent: %s. Current buffer before switch: %s"
           (or agent-name "N/A") (current-buffer))

  ;; Determine if the enhanced chat buffer is active and should be used.
  (let* ((enhanced-chat-buffer-candidate (get-buffer ai-auto-complete-enhanced-chat-buffer-name))
         (use-enhanced-chat-buffer
          (and enhanced-chat-buffer-candidate
               (buffer-live-p enhanced-chat-buffer-candidate)
               (with-current-buffer enhanced-chat-buffer-candidate
                 ai-auto-complete-enhanced-chat-mode)))
         (target-buffer-name
          (if use-enhanced-chat-buffer
              ai-auto-complete-enhanced-chat-buffer-name
            ai-auto-complete-chat-buffer-name))
         (chat-buffer (get-buffer target-buffer-name)))

    (if (not (buffer-live-p chat-buffer))
        (message "Default callback: Chat buffer '%s' is not live or not found. Cannot display response: %s"
                 target-buffer-name
                 (if (and response (> (length response) 100))
                     (concat (substring response 0 100) "...")
                   response))
      (with-current-buffer chat-buffer
        (message "Default callback: Switched to chat buffer: %s." (current-buffer))
        (if use-enhanced-chat-buffer
            ;; Handle response in Enhanced Chat Buffer
            (if (fboundp 'ai-auto-complete-streaming-simulate)
                (progn
                  (message "Default callback: Presenting response in ENHANCED chat buffer '%s'." (buffer-name))
                  (let ((role (if agent-name 'agent 'assistant)))
                    (ai-auto-complete-streaming-simulate response role agent-name)))
              (message "Default callback: Enhanced chat buffer '%s' is active, but 'ai-auto-complete-streaming-simulate' is not found. Response: %s"
                       (buffer-name)
                       (if (and response (> (length response) 100)) (concat (substring response 0 100) "...") response)))
          ;; Handle response in Standard Chat Buffer
          (cond
           ;; Case 1: Standard chat mode with agent system
           ((and ai-auto-complete-chat-mode ; This is buffer-local, checked now we are in chat-buffer
                 (boundp 'ai-auto-complete-agents-enabled)
                 ai-auto-complete-agents-enabled
                 (fboundp 'ai-auto-complete-chat-handle-agent-response))
            (message "Default callback: Presenting response in STANDARD chat via agent system in buffer '%s'." (buffer-name))
            (let ((effective-agent-name (or agent-name
                                           (if (and (boundp 'ai-auto-complete-default-agent)
                                                   ai-auto-complete-default-agent)
                                              ai-auto-complete-default-agent
                                            "chat"))))
              (message "Default callback: Using agent %s for response." effective-agent-name)
              (ai-auto-complete-chat-handle-agent-response effective-agent-name response)))

           ;; Case 2: Standard chat mode (without specific agent handling)
           ((and ai-auto-complete-chat-mode
                 (fboundp 'ai-auto-complete-chat-handle-response))
            (message "Default callback: Presenting response in STANDARD chat interface in buffer '%s'." (buffer-name))
            (ai-auto-complete-chat-handle-response response))

           ;; Case 3: Standard chat mode not active in the target buffer, or handlers missing
           (t
            (message (if agent-name
                         (format "Default callback: Standard chat mode/handlers not available in buffer '%s' for agent '%s'. Response: %s"
                                 (buffer-name) agent-name
                                 (if (and response (> (length response) 100)) (concat (substring response 0 100) "...") response))
                       (format "Default callback: Standard chat mode/handlers not available in buffer '%s'. Response: %s"
                                 (buffer-name)
                                 (if (and response (> (length response) 100)) (concat (substring response 0 100) "...") response)))))))))))



;; Process tool calls in a response
(defun ai-auto-complete-tools-process-response (response callback agent-name)
  "Process tool calls in RESPONSE and call CALLBACK with the result.
If CALLBACK is nil, use the default callback to present results to the user."
  (message "tools-core.el : ai-auto-complete-tools-process-response called with agent-name: %s" (or agent-name "nil"))
  (message "ai-auto-complete-tools-process-response called with agent-name: %s" (or agent-name "nil"))
  (let ((effective-callback (if callback
                               callback
                             (lambda (resp)
                               (ai-auto-complete-tools-default-callback resp agent-name)))))
    (cond
     ;; Check if tools are disabled
     ((not ai-auto-complete-tools-enabled)
      (message "Tools not enabled, returning original response")
      (funcall effective-callback response))

     ;; Check if there are any tool calls in the response
     ((not (string-match-p "<tool name=" response))
      (message "No tool calls found in response, returning original response")
      (funcall effective-callback response))

     ;; Process the response with the state machine
     (t
     (progn
     (message "tools-core.el : ai-auto-complete-tools-process-response Processing response with state machine")
      (ai-auto-complete-tools-process-with-state-machine response effective-callback agent-name))))))

;; Process tool calls recursively
(defun ai-auto-complete-tools-process-response-recursive (response callback depth agent-name)
  "Process tool calls in RESPONSE recursively and call CALLBACK with the result.
   DEPTH tracks the recursion level to prevent infinite loops."
  (message "Processing response recursively at depth %d" depth)
  (message "ai-auto-complete-tools-process-response-recursive called with agent-name: %s" (or agent-name "nil"))
  ;; Ensure we have a valid callback
  (let ((effective-callback (if callback
                               callback
                             (lambda (resp)
                               (ai-auto-complete-tools-default-callback resp agent-name)))))
    ;; Prevent infinite recursion
    (if (> depth 5)
        (progn
          (message "Maximum recursion depth reached, returning current response")
          (funcall effective-callback
                   (concat response "\n\n[ERROR: Maximum tool recursion depth reached. Please continue without further tool calls.]")))
      ;; Check if there are any tool calls in the response
      (if (not (string-match-p "<tool name=" response))
          ;; No tool calls, just return the response
          (progn
            (message "No tool calls found, returning response")
            (funcall effective-callback response))
        ;; Parse and process tool calls
        (let ((tool-calls (ai-auto-complete-tools-parse-response response)))
          (message "Found %d tool calls in response at depth %d" (length tool-calls) depth)
          (if (null tool-calls)
              ;; No tool calls found after parsing (shouldn't happen given the string-match above)
              (progn
                (message "No tool calls found after parsing, returning response")
                (funcall effective-callback response))
            ;; Process tool calls and send results back to LLM
            (ai-auto-complete-tools-execute-and-continue tool-calls response effective-callback depth agent-name)))))))

;; Execute tool calls and continue with LLM
(defun ai-auto-complete-tools-execute-and-continue (tool-calls original-response callback depth agent-name)
  "Execute TOOL-CALLS and continue the conversation with the LLM.
   ORIGINAL-RESPONSE is the LLM's response containing the tool calls.
   CALLBACK is the function to call with the final result.
   DEPTH is the current recursion depth."
  (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
    (message "[TOOLS-DEBUG] Executing %d tool calls at depth %d" (length tool-calls) depth))

  ;; If we've reached the maximum recursion depth, return a simplified response
  (if (> depth 5)
      (progn
        (message "Maximum recursion depth reached, returning simplified response")
        (funcall callback (concat original-response
                                 "\n\n[ERROR: Maximum tool recursion depth reached. Please continue without further tool calls.]")))

    ;; Otherwise, process the tool calls
    (let ((tool-results "")
          (success-count 0)
          (error-count 0))
      ;; Process all tool calls and collect results
      (dolist (tool-call tool-calls)
        (let* ((tool-name (nth 0 tool-call))      ; First element: tool name
               (params (nth 1 tool-call))         ; Second element: parameters
               (tool-id (nth 2 tool-call))        ; Third element: tool-id (may be nil)
               (tool (gethash tool-name ai-auto-complete-tools))
               (tool-fn (and tool (plist-get tool :function))))

          (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
            (message "[TOOLS-DEBUG] Executing tool: %s" tool-name))

          (if tool-fn
              (condition-case err
                  (let ((result (funcall tool-fn params)))
                    (setq success-count (1+ success-count))
                    (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                      (message "[TOOLS-DEBUG] Tool %s executed successfully" tool-name))

                    (setq tool-results (concat tool-results
                                              (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>"
                                                      tool-name result))))
                (error
                 (setq error-count (1+ error-count))
                 (let ((error-msg (format "ERROR executing tool %s: %s"
                                          tool-name (error-message-string err))))
                   (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                     (message "[TOOLS-DEBUG] Tool execution error: %s" error-msg))

                   (setq tool-results (concat tool-results
                                             (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>"
                                                     tool-name error-msg))))))
            (progn
              (setq error-count (1+ error-count))
              (let ((error-msg (format "ERROR: Tool %s not found" tool-name)))
                (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                  (message "[TOOLS-DEBUG] %s" error-msg))

                (setq tool-results (concat tool-results
                                          (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>"
                                                  tool-name error-msg))))))))

      ;; Log summary of tool execution
      (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
        (message "[TOOLS-DEBUG] Tool execution summary: %d successful, %d failed"
                 success-count error-count))

      ;; Prepare the continuation prompt with tool results
      (let* ((response-without-tools (ai-auto-complete-tools-get-response-without-tools original-response))
             ;; Improved continuation prompt with clearer instructions and conversation history
             (continuation-prompt
              (let ((history-text ""))
                ;; Build history text from the conversation history
                (when current-history
                  (setq history-text "\n\nConversation history:\n")
                  (dolist (msg (reverse current-history))
                    (let ((role (car msg))
                          (content (cdr msg)))
                      (setq history-text (concat history-text
                                               (cond
                                                ((eq role 'user) "User: ")
                                                ((eq role 'agent) (format "Agent %s: " (car content)))
                                                ((eq role 'tool-result) "Tool Results: ")
                                                (t "Assistant: "))
                                               (if (eq role 'agent) (cdr content) content)
                                               "\n\n")))))

                (format "I previously responded: \n\n%s\n\n%sI then used tools to get more information. Here are the tool results:%s\n\n%s\n\nBased on these tool results, I should now provide a complete and helpful response that incorporates this information. I should NOT just acknowledge receiving the results or ask what to do next - I should actually use the information to fulfill the user's original request."
                        response-without-tools
                        history-text
                        tool-results
                        (if (> error-count 0)
                            (format "NOTE: %d of %d tool calls had errors."
                                    error-count (length tool-calls))
                          "All tool calls completed successfully."))))
             ;; Use the same backend that was used for the original request
             (backend (if (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode)
                          ai-auto-complete-backend
                        (ai-auto-complete-get-current-backend))))

        ;; Add tool calls and results to conversation history
        (when (and (boundp 'ai-auto-complete--chat-history)
                   ai-auto-complete--chat-history)
          ;; Create a formatted string of tool calls and results for the history
          (let ((tool-history-entry (format "Tool calls executed:\n%s\n\n%s"
                                           tool-results
                                           (if (> error-count 0)
                                               (format "NOTE: %d of %d tool calls had errors."
                                                       error-count (length tool-calls))
                                             "All tool calls completed successfully."))))
            ;; Add the tool calls and results to the history as a 'tool-result entry
            (push (cons 'tool-result tool-history-entry) ai-auto-complete--chat-history)
            (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
              (message "[TOOLS-DEBUG] Added tool calls and results to conversation history")
              (message "[TOOLS-DEBUG] Current history has %d messages" (length ai-auto-complete--chat-history))
              ;; Debug the history content
              (let ((count 0))
                (dolist (hist-msg ai-auto-complete--chat-history)
                  (setq count (1+ count))
                  (message "[TOOLS-DEBUG] History item %d - Type: %s, Role: %s"
                           count
                           (type-of hist-msg)
                           (if (listp hist-msg) (car hist-msg) "unknown")))))))

        ;; Send the continuation prompt to the LLM
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[TOOLS-DEBUG] Sending continuation prompt to LLM"))

        ;; Create a callback for handling the continuation response
        (let ((continuation-handler (lambda (continuation-response)
                                     ;; Check if the continuation response has more tool calls
                                     (if (string-match-p "<tool name=" continuation-response)
                                         ;; If it does, process them recursively
                                         (let ((new-tool-calls (ai-auto-complete-tools-parse-response continuation-response)))
                                           (if new-tool-calls
                                               (ai-auto-complete-tools-execute-and-continue new-tool-calls continuation-response callback (1+ depth) agent-name)
                                             ;; No tool calls found (shouldn't happen given the string-match above)
                                             (funcall callback (concat response-without-tools "\n\n" continuation-response))))
                                       ;; No more tool calls, return the combined response
                                       (funcall callback (concat response-without-tools "\n\n" continuation-response))))))

        ;; Get the current conversation history
        (let ((current-history (when (and (boundp 'ai-auto-complete--chat-history)
                                         ai-auto-complete--chat-history)
                                ai-auto-complete--chat-history)))
          ;; Call the LLM with the continuation prompt
          (message "Passing history with %d messages to continuation"
                   (if current-history (length current-history) 0))
          (ai-auto-complete-complete
           backend
           continuation-prompt
           current-history  ;; Pass the conversation history
           continuation-handler
           agent-name)))))))

;; Get response without tool calls
(defun ai-auto-complete-tools-get-response-without-tools (response)
  "Extract the part of RESPONSE that doesn't contain tool calls.
This function removes all tool tags and their content from RESPONSE,
preserving the rest of the text. It handles multiple tool calls correctly."
  (let ((result response)
        (start 0)
        (removed-count 0))
    ;; Remove all tool tags from the response - updated regex to handle optional id attribute
    (while (string-match "<tool name=\"\\([^\"]+\\)\"\\(?:\\s-+id=\"[^\"]+\"\\)?>" result start)
      (let* ((tool-name (match-string 1 result))
             (tool-start (match-beginning 0))
             (tool-end (string-match "</tool>" result tool-start)))

        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[TOOLS-DEBUG] Removing tool call to %s from response" tool-name))

        (if tool-end
            (progn
              (setq removed-count (1+ removed-count))
              (setq tool-end (+ tool-end (length "</tool>")))
              (setq result (concat (substring result 0 tool-start)
                                   (substring result tool-end)))
              (setq start tool-start))
          (progn
            (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
              (message "[TOOLS-DEBUG] WARNING: No closing </tool> tag found for %s" tool-name))
            (setq start (match-end 0))))))

    ;; Also remove any tool_result tags that might have been included
    (setq start 0)
    (while (string-match "<tool_result name=\"[^\"]+\">" result start)
      (let* ((tool-start (match-beginning 0))
             (tool-end (string-match "</tool_result>" result tool-start)))
        (if tool-end
            (progn
              (setq tool-end (+ tool-end (length "</tool_result>")))
            (setq result (concat (substring result 0 tool-start)
                                  (substring result tool-end)))
              (setq start tool-start))
          ;; If no closing tag found, just move past this opening tag
          (setq start (match-end 0)))))

    (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
      (message "[TOOLS-DEBUG] Removed %d tool calls from response" removed-count))

    ;; Clean up any double newlines that might have been created
    (while (string-match "\n\n\n+" result)
      (setq result (replace-match "\n\n" nil nil result)))

    result))

;; Execute a tool
(defun ai-auto-complete-execute-tool (tool-name params)
  "Execute tool with TOOL-NAME and PARAMS."
  (let ((tool (gethash tool-name ai-auto-complete-tools)))
    (if tool
        (let ((tool-fn (plist-get tool :function)))
          (if tool-fn
              (funcall tool-fn params)
            (format "ERROR: Tool %s has no function defined" tool-name)))
      (format "ERROR: Tool %s not found" tool-name))))

;; Debug function to check if tools are properly registered
(defun ai-auto-complete-tools-debug-info ()
  "Display debug information about registered tools."
  (interactive)
  (message "Tools enabled: %s" ai-auto-complete-tools-enabled)
  (message "Number of registered tools: %d" (hash-table-count ai-auto-complete-tools))
  (message "Registered tools:")

  ;; Avoid nested lambda by using a separate function
  (let ((keys nil)
        (values nil))
    (maphash (lambda (k v)
               (push k keys)
               (push v values))
             ai-auto-complete-tools)
    (cl-loop for name in keys
             for tool in values
             do (message "  - %s: %s" name (plist-get tool :description))))

  (message "Tool definitions:\n%s" (ai-auto-complete-get-tool-definitions)))

;; Function to test if a specific tool is working
(defun ai-auto-complete-test-tool (tool-name &rest params)
  "Test if a specific tool is working.
TOOL-NAME is the name of the tool to test.
PARAMS is an alist of parameters to pass to the tool."
  (interactive
   (let* ((tool-names (let ((names nil))
                        ;; Collect tool names directly to avoid nested lambda
                        (let ((keys nil))
                          (maphash (lambda (k _v) (push k keys)) ai-auto-complete-tools)
                          keys)))
          (tool-name (completing-read "Tool to test: " tool-names))
          (tool (gethash tool-name ai-auto-complete-tools))
          (params-list (plist-get tool :parameters))
          (params nil))
     (dolist (param params-list)
       (let* ((param-name (car param))
              (param-desc (cdr param))
              (param-value (read-string (format "%s (%s): " param-name param-desc))))
         (when (not (string-empty-p param-value))
           (push (cons (intern param-name) param-value) params))))
     (list tool-name params)))

  (message "Testing tool: %s with params: %S" tool-name params)
  (let ((result (ai-auto-complete-execute-tool tool-name params)))
    (with-current-buffer (get-buffer-create "*Tool Test Result*")
      (erase-buffer)
      (insert (format "Tool: %s\n\n" tool-name))
      (insert (format "Parameters: %S\n\n" params))
      (insert "Result:\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

;; Function to enable tools
(defun ai-auto-complete-tools-enable ()
  "Enable tool use in AI Auto Complete."
  (interactive)
  (setq ai-auto-complete-tools-enabled t)
  (message "Tools enabled. Registered tools:")

  ;; Avoid nested lambda by using a separate function
  (let ((keys nil)
        (values nil))
    (maphash (lambda (k v)
               (push k keys)
               (push v values))
             ai-auto-complete-tools)
    (cl-loop for name in keys
             for tool in values
             do (message "  - %s: %s" name (plist-get tool :description))))

  (when (and (fboundp 'advice-member-p)
             (not (advice-member-p 'ai-auto-complete-tools-advice-request 'ai-auto-complete-complete)))
    (advice-add 'ai-auto-complete-complete :around #'ai-auto-complete-tools-advice-request '(:depth 100))
    (message "Added tools advice to ai-auto-complete-complete")))

;; Function to disable tools
(defun ai-auto-complete-tools-disable ()
  "Disable tool use in AI Auto Complete."
  (interactive)
  (setq ai-auto-complete-tools-enabled nil)
  (message "Tools disabled.")
  (when (and (fboundp 'advice-member-p)
             (advice-member-p 'ai-auto-complete-tools-advice-request 'ai-auto-complete-complete))
    (advice-remove 'ai-auto-complete-complete #'ai-auto-complete-tools-advice-request)
    (message "Removed tools advice from ai-auto-complete-complete")))

;; Test function for tool recursion
(defun ai-auto-complete-tools-test-recursion ()
  "Test the tool recursion functionality."
  (interactive)
  ;; Enable tools if not already enabled
  (unless ai-auto-complete-tools-enabled
    (ai-auto-complete-tools-enable))

  ;; Create a test prompt that will likely trigger tool usage
  (let ((test-prompt "I need you to help me with a task that requires using tools. \
                     First, list the contents of the current directory using the list_directory tool. \
                     Then, based on what you find, show me the content of one of the .el files you find."))
    (message "Sending test prompt to LLM: %s" (substring test-prompt 0 50))

    ;; Send the prompt to the current backend
    (let ((backend (ai-auto-complete-get-current-backend)))
      (message "Using backend: %s" backend)
      (ai-auto-complete-complete
       backend
       test-prompt
       nil
       (lambda (response)
         (message "Received final response from LLM")
         (with-current-buffer (get-buffer-create "*AI Tools Test*")
           (erase-buffer)
           (insert "Test Results:\n\n")
           (insert response)
           (display-buffer (current-buffer)))))))
  (message "Test initiated. Results will appear in the *AI Tools Test* buffer."))

;; Test function specifically for multiple tool calls
(defun ai-auto-complete-tools-test-multiple ()
  "Test the multiple tool calls functionality.
This function sends a prompt that explicitly asks the LLM to use
multiple tools in a single response, to test the application's
ability to handle multiple tool calls properly."
  (interactive)
  ;; Enable tools and debug mode
  (unless ai-auto-complete-tools-enabled
    (ai-auto-complete-tools-enable))
  (setq ai-auto-complete-tools-debug-mode t)

  ;; Create a test prompt that will trigger multiple tool usage
  (let ((test-prompt "I need you to perform multiple operations at once using tools:
1. First, list the contents of the current directory using the list_directory tool with path=\".\"
2. Then, in the same response, list the contents of the parent directory using list_directory with path=\"..\"
3. Finally, still in the same response, run the command \"uname -a\" using the run_command tool.

Please make all three tool calls in a single response, and then summarize what you found."))

    (message "Sending multiple tool test prompt to LLM")

    ;; Send the prompt to the current backend
    (let ((backend (ai-auto-complete-get-current-backend)))
      (message "Using backend: %s" backend)
      (ai-auto-complete-complete
       backend
       test-prompt
       nil
       (lambda (response)
         (message "Received final response from LLM for multiple tool test")
         (with-current-buffer (get-buffer-create "*AI Multiple Tools Test*")
           (erase-buffer)
           (insert "Multiple Tool Test Results:\n\n")
           (insert "The following response demonstrates the application's ability to handle multiple tool calls in a single LLM response.\n\n")
           (insert "Test prompt:\n")
           (insert test-prompt)
           (insert "\n\nResponse:\n")
           (insert response)
           (display-buffer (current-buffer)))))))

  (message "Multiple tool test initiated. Results will appear in the *AI Multiple Tools Test* buffer."))

;; Test function for tool_use_id integration
(defun ai-auto-complete-tools-test-tool-use-id ()
  "Test the tool_use_id integration with various XML examples."
  (interactive)

  (let ((test-cases
         '(;; Case 1: Tool with id attribute
           ("<tool name=\"list_files\" id=\"toolu_XYZ123\"><parameters>{\"path\": \".\", \"recursive\": false}</parameters></tool>"
            "Tool with id attribute")

           ;; Case 2: Tool without id attribute (backward compatibility)
           ("<tool name=\"run_command\"><parameters>{\"command\": \"ls -la\"}</parameters></tool>"
            "Tool without id attribute")

           ;; Case 3: Multiple tools with mixed id presence
           ("<tool name=\"tool1\" id=\"toolu_ABC\"><parameters>{\"param1\": \"value1\"}</parameters></tool><tool name=\"tool2\"><parameters>{\"param2\": \"value2\"}</parameters></tool>"
            "Multiple tools with mixed id presence")

           ;; Case 4: Tool with id but malformed parameters
           ("<tool name=\"broken_tool\" id=\"toolu_DEF456\"><parameters>{\"incomplete\": \"json\"</parameters></tool>"
            "Tool with id but malformed parameters"))))

    (with-current-buffer (get-buffer-create "*Tool ID Test*")
      (erase-buffer)
      (insert "Tool Use ID Integration Test\n")
      (insert "===============================\n\n")

      (let ((ai-auto-complete-tools-debug-mode t))
        (dolist (test-case test-cases)
          (let ((xml-input (car test-case))
                (description (cadr test-case)))
            (insert (format "Test: %s\n" description))
            (insert (format "Input: %s\n" xml-input))

            (let ((result (ai-auto-complete-tools-parse-response xml-input)))
              (insert (format "Parsed tool calls: %d\n" (length result)))
              (dolist (tool-call result)
                (insert (format "  - Tool: %s\n" (nth 0 tool-call)))
                (insert (format "    Parameters: %s\n" (prin1-to-string (nth 1 tool-call))))
                (insert (format "    ID: %s\n" (or (nth 2 tool-call) "none"))))
              (insert "\n")))))

      (goto-char (point-min))
      (switch-to-buffer (current-buffer)))))

;; Test function for robust parsing
(defun ai-auto-complete-tools-test-robust-parsing ()
  "Test the robust parsing function with various malformed XML examples."
  (interactive)

  (let ((test-cases
         '(;; Case 1: Missing closing </parameters> tag
           ("<tool name=\"update_task_plan\"><parameters>{\"task_id\": \"123\", \"status\": \"in_progress\"}</tool>"
            "Missing </parameters> tag")

           ;; Case 2: Missing closing </tool> tag
           ("<tool name=\"update_task_plan\"><parameters>{\"task_id\": \"456\"}</parameters>"
            "Missing </tool> tag")

           ;; Case 3: Missing both closing tags
           ("<tool name=\"update_task_plan\"><parameters>{\"task_id\": \"789\"}"
            "Missing both closing tags")

           ;; Case 4: Missing opening <parameters> tag
           ("<tool name=\"update_task_plan\">{\"task_id\": \"abc\"}</tool>"
            "Missing <parameters> tag")

           ;; Case 5: Multiple tools with mixed issues
           ("<tool name=\"tool1\"><parameters>{\"param1\": \"value1\"}</parameters></tool><tool name=\"tool2\"><parameters>{\"param2\": \"value2\"}"
            "Multiple tools with mixed issues")

           ;; Case 6: Simple key:value format
           ("<tool name=\"simple_tool\"><parameters>task_id: \"xyz\"\nstatus: \"done\"</parameters></tool>"
            "Simple key:value format")

           ;; Case 7: Completely malformed but with quoted content
           ("<tool name=\"broken_tool\">some text \"important_value\" more text</tool>"
            "Completely malformed with quoted content"))))

    (with-current-buffer (get-buffer-create "*Tool Parsing Test*")
      (erase-buffer)
      (insert "Tool Parsing Robustness Test\n")
      (insert "==============================\n\n")

      (let ((ai-auto-complete-tools-debug-mode t))
        (dolist (test-case test-cases)
          (let ((xml-input (car test-case))
                (description (cadr test-case)))
            (insert (format "Test: %s\n" description))
            (insert (format "Input: %s\n" xml-input))

            (let ((result (ai-auto-complete-tools-parse-response xml-input)))
              (insert (format "Result: %s\n" (prin1-to-string result)))
              (insert (format "Tool calls found: %d\n\n" (length result))))))

      (goto-char (point-min))
      (switch-to-buffer (current-buffer))))))

(provide 'tools/tools-core)
;;; tools-core.el ends here
